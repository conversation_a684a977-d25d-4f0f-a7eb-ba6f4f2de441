import asyncio
import json
import logging
import random
import re
import time
from dataclasses import dataclass
from typing import Dict, List, Optional

from playwright.async_api import (
    async_playwright,
    TimeoutError as PlaywrightTimeoutError,
    Error as PlaywrightError,
    ViewportSize
)

# 로깅 설정
import coloredlogs

class ContextFilter(logging.Filter):
    def filter(self, record):
        record.proxy = getattr(record, 'proxy', 'N/A')
        record.device = getattr(record, 'device', 'N/A')
        return True

logger = logging.getLogger(__name__)
logger.addFilter(ContextFilter())

class ContextLogger:
    def __init__(self, logger, proxy=None, device=None):
        self.logger = logger
        self.proxy = proxy
        self.device = device
        self.last_log_time = {}
        self.DEBOUNCE_SECONDS = 60  # 동일 메시지 최소 로깅 간격(초)
        
    def log(self, level, msg, *args, **kwargs):
        current_time = time.time()
        msg_hash = hash(msg % args)
        
        # 디바운스 체크
        if msg_hash in self.last_log_time:
            elapsed = current_time - self.last_log_time[msg_hash]
            if elapsed < self.DEBOUNCE_SECONDS:
                return
        
        self.last_log_time[msg_hash] = current_time
        extra = kwargs.pop('extra', {})
        extra.update({'proxy': self.proxy, 'device': self.device})
        self.logger.log(level, msg, *args, extra=extra, **kwargs)
        
    def info(self, msg, *args, **kwargs):
        self.log(logging.INFO, msg, *args, **kwargs)
        
    def warning(self, msg, *args, **kwargs):
        self.log(logging.WARNING, msg, *args, **kwargs)
        
    def error(self, msg, *args, **kwargs):
        self.log(logging.ERROR, msg, *args, **kwargs)
        
    def debug(self, msg, *args, **kwargs):
        self.log(logging.DEBUG, msg, *args, **kwargs)

coloredlogs.install(
    level=logging.INFO,
    fmt='📅 [%(asctime)s] 🌐 [Proxy: %(proxy)s] 📱 [Device: %(device)s] %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    field_styles={
        'asctime': {'color': 'cyan'},
        'levelname': {'bold': True, 'color': 'black'},
        'proxy': {'color': 'blue'},
        'device': {'color': 'magenta'}
    },
    level_styles={
        'debug': {'color': 'green'},
        'info': {'color': 'white'},
        'warning': {'color': 'yellow'},
        'error': {'color': 'red'},
        'critical': {'color': 'red', 'bold': True}
    },
    handlers=[
        logging.FileHandler('naver_search.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

file_handler = next((h for h in logger.handlers if isinstance(h, logging.FileHandler)), None)
if not file_handler:
    file_handler = logging.FileHandler('naver_search.log', encoding='utf-8')
    logger.addHandler(file_handler)
file_handler.setFormatter(logging.Formatter(
    '[%(asctime)s] [Proxy: %(proxy)s] [Device: %(device)s] %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
))

@dataclass
class Config:
    PROXY_USERNAME: str = 'ogh3113'
    PROXY_PASSWORD: str = '@dlwnsdud0720'
    PROXY_FILE: str = "proxy.txt"
    UNFOUND_TARGETS_FILE: str = 'unfound_targets.json'
    NAVER_MOBILE_URL: str = "https://m.naver.com"
    SEARCH_INPUT_SELECTOR: str = '#MM_SEARCH_FAKE'
    MORE_RESULTS_BUTTON_SELECTOR: str = 'a.link_feed_more, a.group_more'
    PAGINATION_SELECTOR_TEMPLATE: str = 'a.pgn'
    PAGE_LOAD_TIMEOUT: int = 45000
    MIN_DELAY: float = 1.0
    MAX_DELAY: float = 3.0
    SEARCH_DELAY_MIN: float = 3.0
    SEARCH_DELAY_MAX: float = 8.0
    PROXY_CHANGE_DELAY_MIN: float = 5.0
    PROXY_CHANGE_DELAY_MAX: float = 10.0
    MAX_PAGINATION_PAGES: int = 10
    MIN_SCROLL_AMOUNT: int = 200
    MAX_SCROLL_AMOUNT: int = 800
    MIN_SCROLL_DURATION: float = 0.5
    MAX_SCROLL_DURATION: float = 2.0

@dataclass
class DeviceConfig:
    user_agent: str
    viewport: ViewportSize
    description: str = ""

config = Config()

TARGET_URL_SELECTORS = [
    'div.api_txt_lines.total_tit a.link_tit',  # api_txt_lines total_tit 안의 제목 링크
]

DEVICE_CONFIGS = [
    DeviceConfig(
        user_agent='Mozilla/5.0 (Linux; Android 14; SM-F946N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36',
        viewport=ViewportSize(width=412, height=915),
        description='Galaxy Z Fold 5 (Unfolded)'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (Linux; Android 14; SM-F731N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36',
        viewport=ViewportSize(width=412, height=915),
        description='Galaxy Z Flip 5'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (Linux; Android 13; SM-F936N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
        viewport=ViewportSize(width=412, height=915),
        description='Galaxy Z Fold 4 (Unfolded)'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (Linux; Android 13; SM-F721N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Mobile Safari/537.36',
        viewport=ViewportSize(width=412, height=915),
        description='Galaxy Z Flip 4'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (iPhone; CPU iPhone OS 17_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4 Mobile/15E148 Safari/604.1',
        viewport=ViewportSize(width=393, height=852),
        description='iPhone 15'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (iPhone; CPU iPhone OS 17_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4 Mobile/15E148 Safari/604.1',
        viewport=ViewportSize(width=430, height=932),
        description='iPhone 15 Pro Max'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.1 Mobile/15E148 Safari/604.1',
        viewport=ViewportSize(width=390, height=844),
        description='iPhone 14 Pro'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (iPhone; CPU iPhone OS 16_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.5 Mobile/15E148 Safari/604.1',
        viewport=ViewportSize(width=428, height=926),
        description='iPhone 14 Plus'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (iPhone; CPU iPhone OS 15_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.7 Mobile/15E148 Safari/604.1',
        viewport=ViewportSize(width=375, height=667),
        description='iPhone SE (3rd gen)'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (iPhone; CPU iPhone OS 14_8 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
        viewport=ViewportSize(width=414, height=896),
        description='iPhone 11'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (Linux; Android 14; SM-S928N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36 KAKAOTALK',
        viewport=ViewportSize(width=384, height=854),
        description='Android 14 KakaoTalk In-app'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (Linux; Android 13; SM-S911N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 KAKAOTALK',
        viewport=ViewportSize(width=360, height=800),
        description='Android 13 KakaoTalk In-app'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (Linux; Android 14; SM-F946N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36 KAKAOTALK',
        viewport=ViewportSize(width=412, height=915),
        description='Z Fold 5 KakaoTalk In-app'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (Linux; Android 14; SM-F731N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36 KAKAOTALK',
        viewport=ViewportSize(width=412, height=915),
        description='Z Flip 5 KakaoTalk In-app'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.1 Mobile/15E148 Safari/604.1 KAKAOTALK',
        viewport=ViewportSize(width=390, height=844),
        description='iPhone 16 KakaoTalk In-app'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.1 Mobile/15E148 Safari/604.1 KAKAOTALK',
        viewport=ViewportSize(width=428, height=926),
        description='iPhone 14 Plus KakaoTalk In-app'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (iPhone; CPU iPhone OS 17_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1 KAKAOTALK',
        viewport=ViewportSize(width=430, height=932),
        description='iPhone 15 Pro Max KakaoTalk In-app'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (Linux; Android 14; SM-S928N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36',
        viewport=ViewportSize(width=412, height=915),
        description='Galaxy S24 Ultra (wider viewport)'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (Linux; Android 11; SM-G998N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.101 Mobile Safari/537.36',
        viewport=ViewportSize(width=384, height=854),
        description='Galaxy S21 Ultra (Android 11)'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (Linux; Android 10; SM-A515N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.127 Mobile Safari/537.36',
        viewport=ViewportSize(width=360, height=780),
        description='Galaxy A51 (Android 10)'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (Linux; Android 13; SM-S901N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
        viewport=ViewportSize(width=360, height=800),
        description='Galaxy S23'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1',
        viewport=ViewportSize(width=390, height=844),
        description='iPhone 13 Pro (iOS 15)'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
        viewport=ViewportSize(width=375, height=812),
        description='iPhone 11 Pro (iOS 14)'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Mobile/15E148 Safari/604.1',
        viewport=ViewportSize(width=390, height=844),
        description='iPhone 14 Pro (iOS 16)'
    )
]

SEARCH_TARGETS = [
    {'keyword': '운암자이포레나', 'domain': 'aryatps.com'},
    {'keyword': '운암자이', 'domain': 'aryatps.com'},
    {'keyword': '일곡공원 위파크 1644-7240', 'domain': 'immodelhouse9.quv.kr'},
    {'keyword': '일곡위파크', 'domain': 'immodelhouse9.quv.kr'},
    {'keyword': '장성 삼계 서현위드184', 'domain': 'damyangkoreaadelium.quv.kr'},
    {'keyword': '장성 바울루체', 'domain': 'immodelhouse10.quv.kr'},
    {'keyword': '대전 엘크루', 'domain': 'immodelhouse3.quv.kr'},
    {'keyword': '정읍 월드메르디앙', 'domain': 'modelhouse7l7.quv.kr'},
    {'keyword': '송암공원 중흥s클래스', 'domain': 'immodelhouse98.quv.kr'},
    {'keyword': '광주 중앙공원 롯데캐슬', 'domain': 'immodelhouse2.quv.kr'},
    {'keyword': '함평 미래프레지안', 'domain': 'modelhouse1d.quv.kr'},
    {'keyword': '봉선 이편한세상', 'domain': 'modelhouse1b.quv.kr'},
    {'keyword': '함평 서현 수와일 리버파크', 'domain': 'immodelhouse7.quv.kr'},
    {'keyword': '동림 우방아이유쉘', 'domain': 'modelhouse2b.quv.kr'},
    {'keyword': '동림2차 우방아이유쉘', 'domain': 'modelhouse2b.quv.kr'},
    {'keyword': '무등산자이앤어울림', 'domain': 'kingofyeosuhonors.quv.kr'},
    {'keyword': '광양 푸르지오 센터파크1644-7240', 'domain': 'modelhouse1g.quv.kr'},
    {'keyword': '광양 푸르지오 센터파크 모델하우스', 'domain': 'modelhouse1g.quv.kr'},
    {'keyword': '더샵광양레이크센텀', 'domain': 'immodelhouse1.quv.kr'},
    {'keyword': '상무 스위첸', 'domain': 'immodelhouse81.quv.kr'},
    {'keyword': '마포 빌리브디에이블', 'domain': 'immodelhouse96.quv.kr'},
    {'keyword': '마포빌리브에이블', 'domain': 'immodelhouse96.quv.kr'},
    {'keyword': '상무 모아미래도', 'domain': 'immodelhouse93.quv.kr'},
    {'keyword': '순천 더포레스트 마루힐', 'domain': 'immodelhouseb.quv.kr'},
    {'keyword': '무인카페 스타벅스', 'domain': 'starbuckskorea.quv.kr'},
    {'keyword': '중외공원 힐스테이트 공식1644-7240', 'domain': 'immodelhome999.quv.kr'},
    {'keyword': '힐스테이트 중외공원 공식', 'domain': 'immodelhome999.quv.kr'},
    {'keyword': '순천 지에이그린웰 예약', 'domain': 'immodelhouse90.quv.kr'},
    {'keyword': '선운2지구 예다음', 'domain': 'goldmodelhouse.quv.kr'},
    {'keyword': '아크로베스티뉴 공식', 'domain': 'inmodelhouse.quv.kr'},
    {'keyword': '광주 한양더힐 공식', 'domain': 'modelhouse1c.quv.kr'},
    {'keyword': '각화 한양더힐1644-7240', 'domain': 'modelhouse1c.quv.kr'},
    {'keyword': '월산 힐스테이트1644 7240', 'domain': 'immodelhouse4.quv.kr'},
    {'keyword': '중앙공원 위파크', 'domain': 'immodelhouse99.quv.kr'},
    {'keyword': '화정 두산위브 모델하우스', 'domain': 'modelhouse1a.quv.kr'},
    {'keyword': '익산역 유탑유블레스', 'domain': 'modelhouse1e.quv.kr'},
    {'keyword': '진월 더리브 라포레', 'domain': 'iamodelhome.quv.kr'},
    {'keyword': '무등산 우방아이유쉘', 'domain': 'thesynergy.quv.kr'},
    {'keyword': '힐스테이트 천호역1644-7240', 'domain': 'immodelhouse91.quv.kr'}
]

async def load_proxies(file_path: str, context_logger=None) -> List[str]:
    log = context_logger if context_logger else logger
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            proxies = [line.strip() for line in f if line.strip()]
        log.info(f"{len(proxies)}개의 프록시를 '{file_path}'에서 로드했습니다.")
        return proxies
    except FileNotFoundError:
        log.error(f"오류: '{file_path}' 파일을 찾을 수 없습니다.")
        return []
    except Exception as e:
        log.error(f"프록시 로드 중 오류 발생: {e}")
        return []

async def human_like_delay(min_seconds: Optional[float] = None, max_seconds: Optional[float] = None, context_logger=None):
    if min_seconds is None:
        min_seconds = config.MIN_DELAY
    if max_seconds is None:
        max_seconds = config.MAX_DELAY
    delay_time = random.uniform(min_seconds, max_seconds)
    log = context_logger if context_logger else logger
    log.debug(f"'{delay_time:.2f}'초 동안 대기합니다.")
    await asyncio.sleep(delay_time)

async def random_scroll(page, context_logger=None):
    scroll_amount = random.randint(config.MIN_SCROLL_AMOUNT, config.MAX_SCROLL_AMOUNT)
    scroll_duration = random.uniform(config.MIN_SCROLL_DURATION, config.MAX_SCROLL_DURATION)
    log = context_logger if context_logger else logger
    log.debug(f"'{scroll_amount}' 픽셀만큼 스크롤하고 '{scroll_duration:.2f}'초 대기합니다.")
    await page.evaluate(f"window.scrollBy(0, {scroll_amount})")
    await asyncio.sleep(scroll_duration)

async def visit_target_url(page, target_url: str, context_logger=None) -> str:
    log = context_logger if context_logger else logger
    try:
        await human_like_delay(2, 4, context_logger)
        for attempt in range(3):
            try:
                await page.goto(target_url, wait_until='domcontentloaded', timeout=config.PAGE_LOAD_TIMEOUT)
                break
            except Exception as e:
                if attempt == 2:
                    raise
                log.warning(f"{attempt+1}차 재시도 중... 오류: {repr(e)}")
                await asyncio.sleep(5 * (attempt + 1))
        await human_like_delay(3, 7, context_logger)
        return target_url
    except PlaywrightTimeoutError:
        log.warning(f"목표 URL '{target_url}' 방문 시간 초과 (TimeoutError).")
        return target_url
    except PlaywrightError as e:
        log.error(f"목표 URL '{target_url}' 방문 중 Playwright 오류 발생: {e}.")
        return target_url
    except Exception as e:
        log.error(f"목표 URL '{target_url}' 방문 중 예상치 못한 오류 발생: {e}.")
        return target_url

async def load_page_and_scroll(page, action_description: str, context_logger=None) -> None:
    log = context_logger if context_logger else logger
    await human_like_delay(2, 8, context_logger)
    await random_scroll(page, context_logger)
    log.info(f"{action_description} - 이동 완료")

async def return_to_naver_home(page, context_logger, reason: str, keyword: str) -> bool:
    context_logger.info(f"{reason}을 위해 네이버 모바일 페이지로 이동.")
    try:
        await page.goto(config.NAVER_MOBILE_URL, timeout=config.PAGE_LOAD_TIMEOUT, wait_until='domcontentloaded')
        await human_like_delay(context_logger=context_logger)
        context_logger.info("네이버 모바일 페이지 로드 완료.")
        return True
    except PlaywrightTimeoutError:
        context_logger.error(f"네이버 모바일 페이지 로드 시간 초과 (TimeoutError) {reason}. 키워드: '{keyword}'")
        return False
    except PlaywrightError as e:
        context_logger.error(f"네이버 모바일 페이지 로드 중 Playwright 오류 발생: {e} {reason}. 키워드: '{keyword}'")
        return False
    except Exception as e:
        context_logger.error(f"네이버 모바일 페이지 로드 중 예상치 못한 오류 발생: {e} {reason}. 키워드: '{keyword}'")
        return False

async def search_and_visit_on_page(page, domain: str, page_description: str, keyword: str, context_logger=None) -> Optional[str]:
    log = context_logger if context_logger else logger
    log.debug(f"대상 도메인: {domain}, 키워드: {keyword}")
    try:
        result = await find_target_url(page, domain, context_logger)
        if result:
            target_url, link_type = result
            visited_url = await visit_target_url(page, target_url, context_logger)
            log.info(f"[방문 성공] {visited_url} ({link_type})")
            return visited_url
        log.info(f"{page_description}에서 목표 URL을 찾지 못함")
        return None
    except Exception as e:
        log.error(f"{page_description} 검색 중 오류 발생: {str(e)}")
        return None

async def find_target_url(page, domain: str, context_logger=None) -> Optional[tuple]:
    log = context_logger if context_logger else logger
    log.info(f"도메인 '{domain}'을 포함하는 목표 URL 검색 시작.")

    # 셀렉터별 링크 타입 매핑
    selector_types = {
        'div.api_txt_lines.total_tit a.link_tit': 'API 텍스트 라인 제목 링크'
    }

    for selector in TARGET_URL_SELECTORS:
        try:
            links = await page.query_selector_all(selector)
            log.info(f"셀렉터 '{selector}': {len(links)}개 링크 발견")

            for idx, link in enumerate(links):
                try:
                    href = await link.get_attribute('href')
                    text = await link.inner_text()
                    log.info(f"링크 {idx+1}: href='{href}', 텍스트='{text.strip()}'")

                    if href and domain in href:
                        link_type = selector_types.get(selector, '알 수 없는 링크')
                        log.info(f"목표 URL 발견: '{href}' ({link_type})")
                        return href, link_type
                except Exception as e:
                    log.warning(f"링크 {idx+1} 처리 중 오류: {e}")
                    continue

        except PlaywrightError as e:
            log.warning(f"셀렉터 '{selector}' 처리 중 Playwright 오류 발생: {e}")
            continue
        except Exception as e:
            log.warning(f"셀렉터 '{selector}' 처리 중 예상치 못한 오류 발생: {e}")
            continue
    log.info(f"도메인 '{domain}'을 포함하는 목표 URL을 찾지 못했습니다.")
    return None

async def search_naver(page, keyword: str, domain: str, context_logger=None) -> Optional[str]:
    log = context_logger if context_logger else logger
    try:
        try:
            await page.fill(config.SEARCH_INPUT_SELECTOR, keyword)
            await human_like_delay(1, 2, context_logger)
            await page.press(config.SEARCH_INPUT_SELECTOR, 'Enter')
            await human_like_delay(5, 9, context_logger)
            await random_scroll(page, context_logger)
        except PlaywrightError as e:
            log.error(f"검색 실행 중 Playwright 오류 발생: {e}. 키워드: '{keyword}'")
            return None
        except Exception as e:
            log.error(f"검색 실행 중 예상치 못한 오류 발생: {e}. 키워드: '{keyword}'")
            return None

        target_url = await search_and_visit_on_page(page, domain, "메인 페이지", keyword, context_logger)
        if target_url:
            return target_url

        log.info("더보기 버튼 검색 시작")
        # 모든 더보기 버튼 가져오기
        more_buttons = await page.query_selector_all(config.MORE_RESULTS_BUTTON_SELECTOR)
        valid_more_button = None

        for button in more_buttons:
            try:
                button_text = await button.inner_text()
                href = await button.get_attribute('href')

                # "검색결과"와 "더보기" 텍스트가 모두 포함되어야 하고, 광고 링크가 아니어야 함
                if (button_text and
                    "검색결과" in button_text and
                    "더보기" in button_text and
                    href and
                    not href.startswith('https://m.ad.search.naver.com/') and
                    not href.startswith('https://fin.land.naver.com/')):
                    valid_more_button = button
                    log.info(f"올바른 검색결과 더보기 버튼 발견")
                    break
            except Exception:
                continue

        if valid_more_button:
            try:
                log.info("검색결과 더보기 버튼 클릭 시도")
                await valid_more_button.click()
                await load_page_and_scroll(page, "'검색결과 더보기' 클릭", context_logger)
                target_url = await search_and_visit_on_page(page, domain, "2 페이지", keyword, context_logger)
                if target_url:
                    return target_url
            except Exception as e:
                log.warning(f"더보기 버튼 클릭 중 오류: {e}")
                pass
        else:
            log.info("더보기 버튼을 찾을 수 없음, 페이지네이션으로 2페이지 이동 시도")
            # 모든 페이지네이션 버튼 가져오기
            pagination_buttons = await page.query_selector_all(config.PAGINATION_SELECTOR_TEMPLATE)
            page_2_button = None

            for button in pagination_buttons:
                try:
                    text = await button.inner_text()
                    if text.strip() == "2":
                        page_2_button = button
                        break
                except Exception:
                    continue

            if page_2_button:
                try:
                    log.info("2페이지 버튼 발견, 클릭 시도")
                    await page_2_button.click()
                    await load_page_and_scroll(page, "더 보기 대신 페이지네이션 사용, 2 페이지", context_logger)
                    target_url = await search_and_visit_on_page(page, domain, "2 페이지", keyword, context_logger)
                    if target_url:
                        return target_url
                except Exception as e:
                    log.warning(f"2페이지 버튼 클릭 중 오류: {e}")
                    pass
            else:
                log.info("2페이지 버튼도 찾을 수 없음")

        # 페이지네이션 버튼들이 로드될 때까지 잠시 대기
        await human_like_delay(2, 4, context_logger)

        log.info(f"3페이지부터 {config.MAX_PAGINATION_PAGES}페이지까지 검색 시작")
        for i in range(3, config.MAX_PAGINATION_PAGES + 1):
            log.info(f"{i}페이지 버튼 검색 시작")

            # 페이지네이션 버튼들이 로드될 때까지 최대 3번 재시도
            target_page_button = None
            for attempt in range(3):
                pagination_buttons = await page.query_selector_all(config.PAGINATION_SELECTOR_TEMPLATE)

                for button in pagination_buttons:
                    try:
                        text = await button.inner_text()
                        if text.strip() == str(i):
                            target_page_button = button
                            break
                    except Exception:
                        continue

                if target_page_button:
                    break

                # 버튼을 찾지 못했으면 잠시 대기 후 재시도
                if attempt < 2:
                    log.info(f"{i}페이지 버튼을 찾지 못함, {attempt+1}초 후 재시도")
                    await asyncio.sleep(attempt + 1)

            if target_page_button:
                try:
                    log.info(f"{i}페이지 버튼 발견, 클릭 시도")
                    await target_page_button.click()
                    await load_page_and_scroll(page, f"{i} 페이지", context_logger)
                    target_url = await search_and_visit_on_page(page, domain, f"{i} 페이지", keyword, context_logger)
                    if target_url:
                        return target_url
                except Exception as e:
                    log.warning(f"{i}페이지 처리 중 오류: {e}")
                    continue
            else:
                log.info(f"{i}페이지 버튼을 찾을 수 없음, 페이지네이션 종료")
                break

        log.debug(f"모든 페이지 검색 완료, 목표 URL을 찾지 못함")
        return None
    except Exception as e:
        log.error(f"'{keyword}' 검색어와 도메인 '{domain}' 검색 중 치명적인 오류 발생: {e}")
        return None

def save_unfound_targets(unfound_targets: List[Dict], file_path: str, context_logger=None) -> None:
    log = context_logger if context_logger else logger
    if unfound_targets:
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(unfound_targets, f, ensure_ascii=False, indent=4)
            log.info(f"목표 URL을 찾지 못한 검색 설정 {len(unfound_targets)}개를 '{file_path}' 파일에 저장했습니다.")
        except Exception as e:
            log.error(f"찾지 못한 검색 설정 저장 중 오류 발생: {e}")
    else:
        log.info("목표 URL을 찾지 못한 검색 설정이 없습니다.")

async def process_browser_tasks(browser, targets: List[Dict], browser_id: int, proxy: str, device_config: DeviceConfig) -> List[Dict]:
    """단일 브라우저에서 할당된 검색 작업들을 처리"""
    unfound_targets = []
    user_agent = device_config.user_agent
    viewport = device_config.viewport
    description = device_config.description

    context_logger = ContextLogger(logger, proxy=proxy, device=f"{description} (브라우저 {browser_id})")
    context_logger.info(f"브라우저 {browser_id} 시작 - {len(targets)}개 작업 할당")

    try:
        context = await browser.new_context(
            user_agent=user_agent,
            viewport=viewport,
            locale='ko-KR',
            permissions=['geolocation'],
            ignore_https_errors=True
        )
        page = await context.new_page()

        try:
            await page.goto(config.NAVER_MOBILE_URL, timeout=config.PAGE_LOAD_TIMEOUT, wait_until='domcontentloaded')
            await human_like_delay(context_logger=context_logger)
        except Exception as e:
            context_logger.error(f"네이버 로드 실패: {e}")
            return targets  # 모든 타겟을 unfound로 반환

        # 네이버 페이지 로드 상태 추적
        is_on_naver_home = True

        for i, target in enumerate(targets):
            keyword = target.get('keyword', '')
            domain = target.get('domain', '')
            if not keyword or not domain:
                continue

            context_logger.info(f"작업 시작: '{keyword}' / '{domain}' ({i+1}/{len(targets)})")

            # 네이버 홈에 있지 않은 경우에만 이동
            if not is_on_naver_home:
                if not await return_to_naver_home(page, context_logger, "작업 시작", keyword):
                    unfound_targets.append(target)
                    continue
                is_on_naver_home = True

            found = await search_naver(page, keyword, domain, context_logger)
            # 검색 실행 후에는 네이버 홈이 아닌 검색 결과 페이지에 있음
            is_on_naver_home = False

            if found:
                context_logger.info(f"성공: '{keyword}' / '{domain}'")
                # 다음 검색을 위해 네이버 홈으로 이동
                if not await return_to_naver_home(page, context_logger, "다음 작업을 위해", keyword):
                    break
                is_on_naver_home = True
            else:
                context_logger.warning(f"실패: '{keyword}' / '{domain}'")
                unfound_targets.append(target)
                # 다음 검색을 위해 네이버 홈으로 이동
                if not await return_to_naver_home(page, context_logger, "다음 작업을 위해", keyword):
                    break
                is_on_naver_home = True

            await human_like_delay(config.SEARCH_DELAY_MIN, config.SEARCH_DELAY_MAX, context_logger)

        await context.close()
        context_logger.info(f"브라우저 {browser_id} 작업 완료")

    except Exception as e:
        context_logger.error(f"브라우저 {browser_id} 처리 중 오류 발생: {e}")
        unfound_targets.extend(targets)

    return unfound_targets

async def main() -> None:
    # 초기 프록시 로드는 전역 logger 사용 (아직 프록시 정보가 없음)
    proxies = await load_proxies(config.PROXY_FILE)
    if not proxies:
        logger.error("프록시 목록이 비어 있습니다. 프로그램을 종료합니다.")
        return

    unfound_targets = []
    async with async_playwright() as p:
        for proxy in proxies:
            from playwright.async_api import ProxySettings
            proxy_config = ProxySettings(
                server=proxy,
                username=config.PROXY_USERNAME,
                password=config.PROXY_PASSWORD
            )

            # 동일한 디바이스 설정 선택
            random_device_raw = random.choice(DEVICE_CONFIGS)
            if isinstance(random_device_raw, dict):
                viewport_data = random_device_raw.get('viewport', {'width': 0, 'height': 0})
                device_config = DeviceConfig(
                    user_agent=random_device_raw.get('user_agent', ''),
                    viewport=ViewportSize(**viewport_data),
                    description=random_device_raw.get('description', 'Unknown Device')
                )
            else:
                device_config = random_device_raw

            context_logger = ContextLogger(logger, proxy=proxy, device=device_config.description)
            context_logger.info(f"프록시 시작: {proxy}")
            context_logger.info(f"디바이스 설정: {device_config.description} - User-Agent='{device_config.user_agent}', Viewport='{device_config.viewport}'")

            # 검색 대상을 2개로 분할
            search_targets_shuffled = SEARCH_TARGETS.copy()
            random.shuffle(search_targets_shuffled)

            mid_point = len(search_targets_shuffled) // 2
            targets_browser1 = search_targets_shuffled[:mid_point]
            targets_browser2 = search_targets_shuffled[mid_point:]

            context_logger.info(f"작업 분할: 브라우저1({len(targets_browser1)}개), 브라우저2({len(targets_browser2)}개)")

            browser1 = None
            browser2 = None
            try:
                # 2개의 브라우저 동시 실행
                browser1 = await p.chromium.launch(
                    headless=False,
                    proxy=proxy_config,
                    args=[
                        '--no-sandbox',
                        '--disable-setuid-sandbox',
                        '--disable-blink-features=AutomationControlled'
                    ]
                )

                browser2 = await p.chromium.launch(
                    headless=False,
                    proxy=proxy_config,
                    args=[
                        '--no-sandbox',
                        '--disable-setuid-sandbox',
                        '--disable-blink-features=AutomationControlled'
                    ]
                )

                # 2개의 브라우저에서 병렬로 작업 실행
                tasks = [
                    process_browser_tasks(browser1, targets_browser1, 1, proxy, device_config),
                    process_browser_tasks(browser2, targets_browser2, 2, proxy, device_config)
                ]

                results = await asyncio.gather(*tasks, return_exceptions=True)

                # 결과 수집
                for result in results:
                    if isinstance(result, Exception):
                        context_logger.error(f"브라우저 작업 중 예외 발생: {result}")
                    elif isinstance(result, list):
                        unfound_targets.extend(result)

            finally:
                if browser1:
                    await browser1.close()
                if browser2:
                    await browser2.close()

            context_logger.info(f"프록시 {proxy} 작업 완료")
            await human_like_delay(config.PROXY_CHANGE_DELAY_MIN, config.PROXY_CHANGE_DELAY_MAX)

    save_unfound_targets(unfound_targets, config.UNFOUND_TARGETS_FILE)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("사용자에 의해 프로그램이 중단되었습니다.")
    except Exception as e:
        logger.error(f"프로그램 실행 중 예상치 못한 오류 발생: {e}")
    finally:
        logger.info("프로그램 종료.")
